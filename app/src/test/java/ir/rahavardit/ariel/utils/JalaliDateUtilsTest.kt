package ir.rahavardit.ariel.utils

import org.junit.Test
import org.junit.Assert.*

/**
 * Test class for JalaliDateUtils.
 */
class JalaliDateUtilsTest {

    @Test
    fun testGregorianToJalali() {
        // Test known date conversion: 2024-01-01 should be 1402/10/11
        val result = JalaliDateUtils.gregorianToJalali(2024, 1, 1)
        assertEquals(1402, result[0]) // Year
        assertEquals(10, result[1])   // Month
        assertEquals(11, result[2])   // Day
    }

    @Test
    fun testJalaliToGregorian() {
        // Test reverse conversion: 1402/10/11 should be 2024-01-01
        val result = JalaliDateUtils.jalaliToGregorian(1402, 10, 11)
        assertEquals(2024, result[0]) // Year
        assertEquals(1, result[1])    // Month
        assertEquals(1, result[2])    // Day
    }

    @Test
    fun testGetCurrentJalaliDate() {
        val currentDate = JalaliDateUtils.getCurrentJalaliDate()
        assertNotNull(currentDate)
        assertTrue(currentDate.matches(Regex("\\d{4}/\\d{2}/\\d{2}")))
    }

    @Test
    fun testGetPersianMonthName() {
        assertEquals("فروردین", JalaliDateUtils.getPersianMonthName(1))
        assertEquals("اردیبهشت", JalaliDateUtils.getPersianMonthName(2))
        assertEquals("اسفند", JalaliDateUtils.getPersianMonthName(12))
        assertEquals("", JalaliDateUtils.getPersianMonthName(13)) // Invalid month
    }

    @Test
    fun testGetDaysInJalaliMonth() {
        // First 6 months have 31 days
        assertEquals(31, JalaliDateUtils.getDaysInJalaliMonth(1403, 1))
        assertEquals(31, JalaliDateUtils.getDaysInJalaliMonth(1403, 6))
        
        // Next 5 months have 30 days
        assertEquals(30, JalaliDateUtils.getDaysInJalaliMonth(1403, 7))
        assertEquals(30, JalaliDateUtils.getDaysInJalaliMonth(1403, 11))
        
        // Last month depends on leap year
        assertEquals(29, JalaliDateUtils.getDaysInJalaliMonth(1403, 12)) // Non-leap year
    }

    @Test
    fun testFormatJalaliDateWithMonthName() {
        val formatted = JalaliDateUtils.formatJalaliDateWithMonthName(1403, 5, 15)
        assertTrue(formatted.contains("خرداد")) // Should contain month name
        assertTrue(formatted.contains("۱۵"))   // Should contain Persian day
        assertTrue(formatted.contains("۱۴۰۳")) // Should contain Persian year
    }

    @Test
    fun testIsValidJalaliDate() {
        assertTrue(JalaliDateUtils.isValidJalaliDate("1403/05/15"))
        assertTrue(JalaliDateUtils.isValidJalaliDate("1400/12/29"))
        
        assertFalse(JalaliDateUtils.isValidJalaliDate("1403/13/15")) // Invalid month
        assertFalse(JalaliDateUtils.isValidJalaliDate("1403/05/32")) // Invalid day
        assertFalse(JalaliDateUtils.isValidJalaliDate("invalid"))    // Invalid format
    }
}
