<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/dialog_background">

    <!-- Title -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/select_date"
        android:textAppearance="?attr/textAppearanceHeadline6"
        android:textColor="?attr/colorOnSurface"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Date Pickers Container -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginBottom="24dp">

        <!-- Year Picker -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/year"
                android:textAppearance="?attr/textAppearanceCaption"
                android:textColor="?attr/colorOnSurface"
                android:layout_marginBottom="8dp" />

            <NumberPicker
                android:id="@+id/year_picker"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:theme="@style/NumberPickerStyle" />

        </LinearLayout>

        <!-- Month Picker -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/month"
                android:textAppearance="?attr/textAppearanceCaption"
                android:textColor="?attr/colorOnSurface"
                android:layout_marginBottom="8dp" />

            <NumberPicker
                android:id="@+id/month_picker"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:theme="@style/NumberPickerStyle" />

        </LinearLayout>

        <!-- Day Picker -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/day"
                android:textAppearance="?attr/textAppearanceCaption"
                android:textColor="?attr/colorOnSurface"
                android:layout_marginBottom="8dp" />

            <NumberPicker
                android:id="@+id/day_picker"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:theme="@style/NumberPickerStyle" />

        </LinearLayout>

    </LinearLayout>

    <!-- Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_cancel"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/cancel"
            android:layout_marginEnd="8dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_ok"
            style="@style/Widget.MaterialComponents.Button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/ok" />

    </LinearLayout>

</LinearLayout>
