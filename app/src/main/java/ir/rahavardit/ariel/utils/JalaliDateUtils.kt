package ir.rahavardit.ariel.utils

import java.util.Calendar
import java.util.Date

/**
 * Utility class for Jalali (Persian) date operations.
 * Self-contained implementation without external dependencies.
 */
object JalaliDateUtils {

    /**
     * Converts a Gregorian date to Jalali date.
     *
     * @param gYear Gregorian year
     * @param gMonth Gregorian month (1-12)
     * @param gDay Gregorian day
     * @return IntArray with [jalaliYear, jalaliMonth, jalaliDay]
     */
    fun gregorianToJalali(gYear: Int, gMonth: Int, gDay: Int): IntArray {
        val gDaysInMonth = intArrayOf(31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31)
        val jDaysInMonth = intArrayOf(31, 31, 31, 31, 31, 31, 30, 30, 30, 30, 30, 29)

        var jYear: Int
        var jMonth: Int
        var jDay: Int

        val gDayNo = 365 * gYear + ((gYear + 3) / 4) - ((gYear + 99) / 100) + ((gYear + 399) / 400) - 80 + gDay +
                     (0 until gMonth - 1).sumOf {
                         gDaysInMonth[it] + if (it == 1 && isGregorianLeapYear(gYear)) 1 else 0
                     }

        jYear = -1029 + 33 * (gDayNo / 12053)
        gDayNo %= 12053

        jYear += 4 * (gDayNo / 1461)
        gDayNo %= 1461

        if (gDayNo > 365) {
            jYear += (gDayNo - 1) / 365
            gDayNo = (gDayNo - 1) % 365
        }

        if (gDayNo < 186) {
            jMonth = 1 + gDayNo / 31
            jDay = 1 + (gDayNo % 31)
        } else {
            jMonth = 7 + (gDayNo - 186) / 30
            jDay = 1 + ((gDayNo - 186) % 30)
        }

        return intArrayOf(jYear, jMonth, jDay)
    }

    /**
     * Converts a Jalali date to Gregorian date.
     *
     * @param jYear Jalali year
     * @param jMonth Jalali month (1-12)
     * @param jDay Jalali day
     * @return IntArray with [gregorianYear, gregorianMonth, gregorianDay]
     */
    fun jalaliToGregorian(jYear: Int, jMonth: Int, jDay: Int): IntArray {
        val gDaysInMonth = intArrayOf(31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31)
        val jDaysInMonth = intArrayOf(31, 31, 31, 31, 31, 31, 30, 30, 30, 30, 30, 29)

        var gYear: Int
        var gMonth: Int
        var gDay: Int

        val jDayNo = 365 * jYear + ((jYear / 33) * 8) + (((jYear % 33) + 3) / 4) + 78 + jDay +
                     (0 until jMonth - 1).sumOf {
                         jDaysInMonth[it] + if (it == 11 && isJalaliLeapYear(jYear)) 1 else 0
                     }

        gYear = 1600 + 400 * (jDayNo / 146097)
        var gDayNo = jDayNo % 146097

        var leap = true
        if (gDayNo >= 36525) {
            gDayNo--
            gYear += 100 * (gDayNo / 36524)
            gDayNo %= 36524
            if (gDayNo >= 365) gDayNo++
        }

        gYear += 4 * (gDayNo / 1461)
        gDayNo %= 1461

        if (gDayNo >= 366) {
            leap = false
            gDayNo--
            gYear += gDayNo / 365
            gDayNo = gDayNo % 365
        }

        var i = 0
        while (i < gDaysInMonth.size && gDayNo >= gDaysInMonth[i] + if (i == 1 && leap) 1 else 0) {
            gDayNo -= gDaysInMonth[i] + if (i == 1 && leap) 1 else 0
            i++
        }
        gMonth = i + 1
        gDay = gDayNo + 1

        return intArrayOf(gYear, gMonth, gDay)
    }

    /**
     * Checks if a Gregorian year is leap.
     */
    private fun isGregorianLeapYear(year: Int): Boolean {
        return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)
    }

    /**
     * Checks if a Jalali year is leap.
     */
    private fun isJalaliLeapYear(year: Int): Boolean {
        val breaks = intArrayOf(
            -14, 3, 13, 84, 111, 138, 165, 192, 219, 246, 273, 300, 327, 354, 381, 408,
            435, 462, 489, 516, 543, 570, 597, 624, 651, 678, 705, 732, 759, 786, 813,
            840, 867, 894, 921, 948, 975, 1002, 1029, 1056, 1083, 1110, 1137, 1164,
            1191, 1218, 1245, 1272, 1299, 1326, 1353, 1380, 1407, 1434, 1461, 1488,
            1515, 1542, 1569, 1596, 1623, 1650
        )

        val gy = year + 1029
        var leap = -14
        var jp = breaks[0]

        var jump = 0
        for (j in 1 until breaks.size) {
            val jm = breaks[j]
            jump = jm - jp
            if (year < jm) break
            leap += (jump / 33) * 8 + ((jump % 33) / 4)
            jp = jm
        }

        val n = year - jp
        if (n < jump) {
            leap += ((n - 1) / 33) * 8 + (((n - 1) % 33 + 3) / 4)
            if ((jump % 33) == 4 && (jump - n) == 4) leap++
        }

        return (leap + 4) % 33 < 5
    }

    /**
     * Gets the current Jalali date as a string in YYYY/MM/DD format.
     *
     * @return Current Jalali date string
     */
    fun getCurrentJalaliDate(): String {
        val calendar = Calendar.getInstance()
        val jalali = gregorianToJalali(
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH) + 1, // Calendar.MONTH is 0-based
            calendar.get(Calendar.DAY_OF_MONTH)
        )
        return String.format("%04d/%02d/%02d", jalali[0], jalali[1], jalali[2])
    }

    /**
     * Formats a Jalali date for display with Persian numerals.
     *
     * @param jalaliDate Jalali date string in YYYY/MM/DD format
     * @return Formatted date string with Persian numerals
     */
    fun formatJalaliDateForDisplay(jalaliDate: String): String {
        return PersianUtils.convertToPersianNumerals(jalaliDate)
    }

    /**
     * Gets the number of days in a Jalali month.
     *
     * @param year Jalali year
     * @param month Jalali month (1-12)
     * @return Number of days in the month
     */
    fun getDaysInJalaliMonth(year: Int, month: Int): Int {
        return when (month) {
            in 1..6 -> 31  // First 6 months have 31 days
            in 7..11 -> 30 // Next 5 months have 30 days
            12 -> if (isJalaliLeapYear(year)) 30 else 29 // Last month depends on leap year
            else -> 30
        }
    }

    /**
     * Gets the Persian name of a month.
     *
     * @param month Month number (1-12)
     * @return Persian month name
     */
    fun getPersianMonthName(month: Int): String {
        val monthNames = arrayOf(
            "فروردین", "اردیبهشت", "خرداد", "تیر", "مرداد", "شهریور",
            "مهر", "آبان", "آذر", "دی", "بهمن", "اسفند"
        )
        return if (month in 1..12) monthNames[month - 1] else ""
    }

    /**
     * Gets the Persian name of a day of week.
     *
     * @param dayOfWeek Day of week (1=Saturday, 7=Friday in Persian calendar)
     * @return Persian day name
     */
    fun getPersianDayName(dayOfWeek: Int): String {
        val dayNames = arrayOf(
            "شنبه", "یکشنبه", "دوشنبه", "سه‌شنبه", "چهارشنبه", "پنج‌شنبه", "جمعه"
        )
        return if (dayOfWeek in 1..7) dayNames[dayOfWeek - 1] else ""
    }

    /**
     * Validates a Jalali date string format.
     *
     * @param jalaliDate Date string to validate
     * @return true if valid, false otherwise
     */
    fun isValidJalaliDate(jalaliDate: String): Boolean {
        return try {
            val parts = jalaliDate.split("/")
            if (parts.size != 3) return false
            
            val year = parts[0].toInt()
            val month = parts[1].toInt()
            val day = parts[2].toInt()
            
            // Basic validation
            year in 1300..1500 && month in 1..12 && day in 1..31
        } catch (e: Exception) {
            false
        }
    }

    /**
     * Converts Jalali date components to a formatted display string.
     *
     * @param year Jalali year
     * @param month Jalali month
     * @param day Jalali day
     * @return Formatted display string with Persian numerals and month name
     */
    fun formatJalaliDateWithMonthName(year: Int, month: Int, day: Int): String {
        val monthName = getPersianMonthName(month)
        val persianDay = PersianUtils.convertToPersianNumerals(day)
        val persianYear = PersianUtils.convertToPersianNumerals(year)
        return "$persianDay $monthName $persianYear"
    }
}
