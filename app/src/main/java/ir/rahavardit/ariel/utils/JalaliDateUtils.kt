package ir.rahavardit.ariel.utils

import saman.zamani.persiandate.PersianDate
import saman.zamani.persiandate.PersianDateFormat
import java.util.Calendar
import java.util.Date

/**
 * Utility class for Jalali (Persian) date operations.
 */
object JalaliDateUtils {

    /**
     * Converts a Gregorian date to Jalali date string in YYYY/MM/DD format.
     *
     * @param year Gregorian year
     * @param month Gregorian month (0-based, as used in Calendar)
     * @param dayOfMonth Gregorian day of month
     * @return Jalali date string in YYYY/MM/DD format
     */
    fun gregorianToJalali(year: Int, month: Int, dayOfMonth: Int): String {
        val calendar = Calendar.getInstance()
        calendar.set(year, month, dayOfMonth)
        val persianDate = PersianDate(calendar.time)
        
        return String.format(
            "%04d/%02d/%02d",
            persianDate.shYear,
            persianDate.shMonth,
            persianDate.shDay
        )
    }

    /**
     * Converts a Date object to Jalali date string in YYYY/MM/DD format.
     *
     * @param date The Date object to convert
     * @return Jalali date string in YYYY/MM/DD format
     */
    fun dateToJalali(date: Date): String {
        val persianDate = PersianDate(date)
        return String.format(
            "%04d/%02d/%02d",
            persianDate.shYear,
            persianDate.shMonth,
            persianDate.shDay
        )
    }

    /**
     * Converts a Jalali date string to a Date object.
     *
     * @param jalaliDate Jalali date string in YYYY/MM/DD format
     * @return Date object
     */
    fun jalaliToDate(jalaliDate: String): Date {
        val parts = jalaliDate.split("/")
        if (parts.size != 3) {
            throw IllegalArgumentException("Invalid Jalali date format. Expected YYYY/MM/DD")
        }
        
        val year = parts[0].toInt()
        val month = parts[1].toInt()
        val day = parts[2].toInt()
        
        val persianDate = PersianDate()
        persianDate.shYear = year
        persianDate.shMonth = month
        persianDate.shDay = day
        
        return persianDate.toDate()
    }

    /**
     * Gets the current Jalali date as a string in YYYY/MM/DD format.
     *
     * @return Current Jalali date string
     */
    fun getCurrentJalaliDate(): String {
        val persianDate = PersianDate()
        return String.format(
            "%04d/%02d/%02d",
            persianDate.shYear,
            persianDate.shMonth,
            persianDate.shDay
        )
    }

    /**
     * Formats a Jalali date for display with Persian numerals.
     *
     * @param jalaliDate Jalali date string in YYYY/MM/DD format
     * @return Formatted date string with Persian numerals
     */
    fun formatJalaliDateForDisplay(jalaliDate: String): String {
        return PersianUtils.convertToPersianNumerals(jalaliDate)
    }

    /**
     * Gets the Persian name of a month.
     *
     * @param month Month number (1-12)
     * @return Persian month name
     */
    fun getPersianMonthName(month: Int): String {
        val monthNames = arrayOf(
            "فروردین", "اردیبهشت", "خرداد", "تیر", "مرداد", "شهریور",
            "مهر", "آبان", "آذر", "دی", "بهمن", "اسفند"
        )
        return if (month in 1..12) monthNames[month - 1] else ""
    }

    /**
     * Gets the Persian name of a day of week.
     *
     * @param dayOfWeek Day of week (1=Saturday, 7=Friday in Persian calendar)
     * @return Persian day name
     */
    fun getPersianDayName(dayOfWeek: Int): String {
        val dayNames = arrayOf(
            "شنبه", "یکشنبه", "دوشنبه", "سه‌شنبه", "چهارشنبه", "پنج‌شنبه", "جمعه"
        )
        return if (dayOfWeek in 1..7) dayNames[dayOfWeek - 1] else ""
    }

    /**
     * Validates a Jalali date string format.
     *
     * @param jalaliDate Date string to validate
     * @return true if valid, false otherwise
     */
    fun isValidJalaliDate(jalaliDate: String): Boolean {
        return try {
            val parts = jalaliDate.split("/")
            if (parts.size != 3) return false
            
            val year = parts[0].toInt()
            val month = parts[1].toInt()
            val day = parts[2].toInt()
            
            // Basic validation
            year in 1300..1500 && month in 1..12 && day in 1..31
        } catch (e: Exception) {
            false
        }
    }

    /**
     * Converts Jalali date components to a formatted display string.
     *
     * @param year Jalali year
     * @param month Jalali month
     * @param day Jalali day
     * @return Formatted display string with Persian numerals and month name
     */
    fun formatJalaliDateWithMonthName(year: Int, month: Int, day: Int): String {
        val monthName = getPersianMonthName(month)
        val persianDay = PersianUtils.convertToPersianNumerals(day)
        val persianYear = PersianUtils.convertToPersianNumerals(year)
        return "$persianDay $monthName $persianYear"
    }
}
