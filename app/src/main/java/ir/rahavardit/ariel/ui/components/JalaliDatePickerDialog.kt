package ir.rahavardit.ariel.ui.components

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.widget.NumberPicker
import androidx.appcompat.app.AlertDialog
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.databinding.DialogJalaliDatePickerBinding
import ir.rahavardit.ariel.utils.JalaliDateUtils
import ir.rahavardit.ariel.utils.PersianUtils
import saman.zamani.persiandate.PersianDate

/**
 * Custom Jalali date picker dialog.
 */
class JalaliDatePickerDialog(
    context: Context,
    private val onDateSelected: (year: Int, month: Int, day: Int) -> Unit,
    private val initialYear: Int = PersianDate().shYear,
    private val initialMonth: Int = PersianDate().shMonth,
    private val initialDay: Int = PersianDate().shDay
) : Dialog(context) {

    private lateinit var binding: DialogJalaliDatePickerBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = DialogJalaliDatePickerBinding.inflate(LayoutInflater.from(context))
        setContentView(binding.root)

        setupDatePickers()
        setupButtons()
        
        // Set dialog properties
        window?.setLayout(
            (context.resources.displayMetrics.widthPixels * 0.9).toInt(),
            android.view.ViewGroup.LayoutParams.WRAP_CONTENT
        )
    }

    private fun setupDatePickers() {
        // Setup year picker
        val currentYear = PersianDate().shYear
        val yearRange = (currentYear - 10)..(currentYear + 10)
        
        binding.yearPicker.apply {
            minValue = 0
            maxValue = yearRange.count() - 1
            displayedValues = yearRange.map { PersianUtils.convertToPersianNumerals(it) }.toTypedArray()
            value = yearRange.indexOf(initialYear)
            descendantFocusability = NumberPicker.FOCUS_BLOCK_DESCENDANTS
        }

        // Setup month picker
        val monthNames = (1..12).map { JalaliDateUtils.getPersianMonthName(it) }.toTypedArray()
        
        binding.monthPicker.apply {
            minValue = 0
            maxValue = 11
            displayedValues = monthNames
            value = initialMonth - 1
            descendantFocusability = NumberPicker.FOCUS_BLOCK_DESCENDANTS
        }

        // Setup day picker
        updateDayPicker()

        // Add listeners to update day picker when year or month changes
        binding.yearPicker.setOnValueChangedListener { _, _, _ -> updateDayPicker() }
        binding.monthPicker.setOnValueChangedListener { _, _, _ -> updateDayPicker() }
    }

    private fun updateDayPicker() {
        val selectedYear = getSelectedYear()
        val selectedMonth = getSelectedMonth()
        val daysInMonth = getDaysInMonth(selectedYear, selectedMonth)
        
        val currentDay = binding.dayPicker.value + 1
        
        binding.dayPicker.apply {
            minValue = 0
            maxValue = daysInMonth - 1
            displayedValues = (1..daysInMonth).map { PersianUtils.convertToPersianNumerals(it) }.toTypedArray()
            
            // Preserve current day if possible, otherwise set to last day of month
            value = if (currentDay <= daysInMonth) currentDay - 1 else daysInMonth - 1
            descendantFocusability = NumberPicker.FOCUS_BLOCK_DESCENDANTS
        }
    }

    private fun getDaysInMonth(year: Int, month: Int): Int {
        return when (month) {
            in 1..6 -> 31  // First 6 months have 31 days
            in 7..11 -> 30 // Next 5 months have 30 days
            12 -> if (isLeapYear(year)) 30 else 29 // Last month depends on leap year
            else -> 30
        }
    }

    private fun isLeapYear(year: Int): Boolean {
        // Persian leap year calculation
        val breaks = intArrayOf(
            -14, 3, 13, 84, 111, 138, 165, 192, 219, 246, 273, 300, 327, 354, 381, 408,
            435, 462, 489, 516, 543, 570, 597, 624, 651, 678, 705, 732, 759, 786, 813,
            840, 867, 894, 921, 948, 975, 1002, 1029, 1056, 1083, 1110, 1137, 1164,
            1191, 1218, 1245, 1272, 1299, 1326, 1353, 1380, 1407, 1434, 1461, 1488,
            1515, 1542, 1569, 1596, 1623, 1650
        )
        
        val gy = year + 1029
        var leap = -14
        var jp = breaks[0]
        
        var jump = 0
        for (j in 1 until breaks.size) {
            val jm = breaks[j]
            jump = jm - jp
            if (year < jm) break
            leap += (jump / 33) * 8 + ((jump % 33) / 4)
            jp = jm
        }
        
        val n = year - jp
        if (n < jump) {
            leap += ((n - 1) / 33) * 8 + (((n - 1) % 33 + 3) / 4)
            if ((jump % 33) == 4 && (jump - n) == 4) leap++
        }
        
        return (leap + 4) % 33 < 5
    }

    private fun setupButtons() {
        binding.btnCancel.setOnClickListener {
            dismiss()
        }

        binding.btnOk.setOnClickListener {
            val year = getSelectedYear()
            val month = getSelectedMonth()
            val day = getSelectedDay()
            
            onDateSelected(year, month, day)
            dismiss()
        }
    }

    private fun getSelectedYear(): Int {
        val currentYear = PersianDate().shYear
        val yearRange = (currentYear - 10)..(currentYear + 10)
        return yearRange.elementAt(binding.yearPicker.value)
    }

    private fun getSelectedMonth(): Int {
        return binding.monthPicker.value + 1
    }

    private fun getSelectedDay(): Int {
        return binding.dayPicker.value + 1
    }
}
